# Email Service Setup Guide

This guide explains how to configure the automatic email service for invoice sending using AWS SES and Handlebars templates.

## Features

- ✅ Automatic invoice email sending when invoices are generated
- ✅ Beautiful HTML email templates (Bulgarian and English)
- ✅ PDF invoice attachments
- ✅ AWS SES integration
- ✅ Language detection based on client country
- ✅ Handlebars template engine
- ✅ Email configuration testing endpoint

## Environment Variables

Add the following environment variables to your `.env` file:

```bash
# AWS SES Configuration
AWS_ACCESS_KEY_ID=your_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key
AWS_SES_REGION=eu-central-1

# Email Configuration
FROM_EMAIL=<EMAIL>
FROM_NAME=Chainmatic
COMPANY_NAME=Chainmatic

# Optional: Test email for configuration testing
TEST_EMAIL=<EMAIL>
```

## AWS SES Setup

### 1. Create AWS SES Account
1. Go to AWS Console → Simple Email Service (SES)
2. Choose your region (e.g., `eu-central-1`)
3. Verify your domain or email address

### 2. Create IAM User for SES
1. Go to AWS Console → IAM → Users
2. Create new user with programmatic access
3. Attach the `AmazonSESFullAccess` policy
4. Save the Access Key ID and Secret Access Key

### 3. Verify Email Addresses
- In SES Console, verify the sender email address (`FROM_EMAIL`)
- If in sandbox mode, also verify recipient email addresses

### 4. Request Production Access (Optional)
- By default, SES is in sandbox mode (can only send to verified emails)
- Request production access to send to any email address

## Email Templates

The service includes two Handlebars templates:

### Bulgarian Template (`invoice-email-bg.hbs`)
- Used when client country is Bulgaria
- Bulgarian language content
- Professional styling with company branding

### English Template (`invoice-email-en.hbs`)
- Used for all other countries
- English language content
- Same professional styling

## How It Works

### Automatic Email Sending
1. When an invoice is successfully generated via `InvoiceGeneratorService`
2. The service automatically:
   - Determines language based on client's country
   - Downloads the invoice PDF from inv.bg
   - Compiles the appropriate email template
   - Sends email with PDF attachment via AWS SES

### Language Detection
- **Bulgarian**: When client's billing country is "Bulgaria"
- **English**: For all other countries

### Email Content
Each email includes:
- Invoice number and type (initial/final)
- Project title and proposal ID
- Amount and currency
- Due date
- PDF invoice attachment
- Professional company branding

## Testing

### Test Email Configuration
Send a POST request to test the email setup:

```bash
curl -X POST http://localhost:4200/api/proposals/test-email \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

Response:
```json
{
  "success": true,
  "message": "Email configuration test successful"
}
```

### Manual Invoice Generation
You can trigger invoice generation (which will send emails) via:

```bash
curl -X POST http://localhost:4200/api/proposals/invoices \
  -H "Content-Type: application/json" \
  -d '{"proposal_id": "your-proposal-id"}'
```

## Troubleshooting

### Common Issues

1. **Email not sending**
   - Check AWS credentials are correct
   - Verify sender email address in SES
   - Check SES region matches your configuration

2. **Template errors**
   - Ensure templates directory exists: `client/src/api/templates/`
   - Check Handlebars syntax in templates

3. **PDF attachment issues**
   - Verify inv.bg API is working
   - Check invoice ID exists in database

### Logs
Check console logs for detailed error messages:
- Email service initialization
- Template loading
- AWS SES sending status
- PDF download status

## File Structure

```
src/api/
├── services/
│   ├── email.service.ts          # Main email service
│   ├── invoice-generator.service.ts  # Updated with email integration
│   └── index.ts                  # Service exports
├── templates/
│   ├── invoice-email-bg.hbs      # Bulgarian email template
│   └── invoice-email-en.hbs      # English email template
└── routes/
    └── proposals.routes.ts       # Added test endpoint
```

## Security Notes

- Never expose AWS credentials in client-side code
- Use environment variables for all sensitive configuration
- Consider using AWS IAM roles in production
- Regularly rotate AWS access keys

## Production Considerations

1. **SES Limits**: Monitor sending quotas and rates
2. **Error Handling**: Implement retry logic for failed emails
3. **Monitoring**: Set up CloudWatch alerts for SES metrics
4. **Compliance**: Ensure GDPR compliance for email communications
5. **Unsubscribe**: Consider adding unsubscribe functionality if needed

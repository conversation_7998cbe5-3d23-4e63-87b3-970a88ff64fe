<!DOCTYPE html>
<html lang="{{#if (eq language 'bg')}}bg{{else}}en{{/if}}">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>{{#if (eq language 'bg')}}Фактура{{else}}Invoice{{/if}} {{invoiceNumber}}</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 600px;
      margin: 0 auto;
      padding: 20px;
      background-color: #f9f9f9;
    }

    .container {
      background-color: white;
      padding: 30px;
      border-radius: 10px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .header {
      text-align: center;
      border-bottom: 3px solid #635bff;
      padding-bottom: 20px;
      margin-bottom: 30px;
    }

    .logo {
      font-size: 28px;
      font-weight: bold;
      color: #635bff;
      margin-bottom: 10px;
    }

    .invoice-title {
      font-size: 24px;
      color: #333;
      margin: 20px 0;
    }

    .invoice-details {
      background-color: #f8f9fa;
      padding: 20px;
      border-radius: 8px;
      margin: 20px 0;
    }

    .detail-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
      padding: 10px 0;
      border-bottom: 1px solid #f0f0f0;
    }

    .detail-label {
      font-weight: bold;
      color: #555;
      flex: 1;
      text-align: left;
    }

    .detail-value {
      color: #333;
      flex: 1;
      text-align: right;
    }

    .proposal-id {
      font-family: 'Courier New', monospace;
      font-size: 13px;
      background-color: #f5f0ff;
      padding: 6px 10px;
      border-radius: 4px;
      border: 1px solid #d7b4f4;
      word-break: break-all;
      line-height: 1.4;
      max-width: 200px;
    }

    .amount {
      font-size: 16px;
      font-weight: bold;
      color: #7125bb;
      background-color: #f5f0ff;
      padding: 8px 12px;
      border-radius: 6px;
      border: 2px solid #7125bb;
    }

    .message {
      margin: 25px 0;
      padding: 20px;
      background-color: #f5f0ff;
      border-left: 4px solid #635bff;
      border-radius: 4px;
    }

    .footer {
      margin-top: 30px;
      padding-top: 20px;
      border-top: 1px solid #eee;
      text-align: center;
      color: #666;
      font-size: 14px;
    }

    .button {
      display: inline-block;
      padding: 12px 24px;
      background-color: #635bff;
      color: white;
      text-decoration: none;
      border-radius: 5px;
      margin: 15px 0;
      font-weight: bold;
    }

    .button:hover {
      background-color: #5046e5;
    }

    /* Mobile optimizations */
    @media (max-width: 600px) {
      body {
        padding: 10px;
      }

      .container {
        padding: 20px;
      }

      .detail-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
      }

      .detail-label {
        text-align: left;
        margin-bottom: 4px;
      }

      .detail-value {
        text-align: left;
        width: 100%;
      }

      .proposal-id {
        max-width: 100%;
        font-size: 12px;
      }

      .amount {
        font-size: 14px;
        padding: 6px 10px;
      }

      .logo {
        font-size: 24px;
      }

      .invoice-title {
        font-size: 20px;
      }
    }

    /* Extra small screens */
    @media (max-width: 400px) {
      .container {
        padding: 15px;
      }

      .invoice-details {
        padding: 15px;
      }

      .proposal-id {
        font-size: 11px;
        padding: 4px 8px;
      }
    }
  </style>
</head>
<body>
<div class="container">
  <div class="header">
    <div class="logo">{{companyName}}</div>
    <h1 class="invoice-title">
      {{#if (eq language 'bg')}}
        Фактура {{invoiceNumber}}
      {{else}}
        Invoice {{invoiceNumber}}
      {{/if}}
    </h1>
  </div>

  <p>
    {{#if (eq language 'bg')}}
      Уважаеми {{clientName}},
    {{else}}
      Dear {{clientName}},
    {{/if}}
  </p>

  <p>
    {{#if (eq language 'bg')}}
      Изпращаме Ви фактурата за проект <strong>{{projectTitle}}</strong>.
    {{else}}
      Please find attached the invoice for project <strong>{{projectTitle}}</strong>.
    {{/if}}
  </p>

  <div class="invoice-details">
    <div class="detail-row">
      <span class="detail-label">
        {{#if (eq language 'bg')}}
          Номер на фактура:
        {{else}}
          Invoice Number:
        {{/if}}
      </span>
      <span class="detail-value">{{invoiceNumber}}</span>
    </div>
    
    <div class="detail-row">
      <span class="detail-label">
        {{#if (eq language 'bg')}}
          Тип плащане:
        {{else}}
          Payment Type:
        {{/if}}
      </span>
      <span class="detail-value">
        {{#if (eq invoiceType 'initial')}}
          {{#if (eq language 'bg')}}
            Първоначално плащане
          {{else}}
            Initial Payment
          {{/if}}
        {{else}}
          {{#if (eq language 'bg')}}
            Финално плащане
          {{else}}
            Final Payment
          {{/if}}
        {{/if}}
      </span>
    </div>
    
    <div class="detail-row">
      <span class="detail-label">
        {{#if (eq language 'bg')}}
          Проект:
        {{else}}
          Project:
        {{/if}}
      </span>
      <span class="detail-value">{{projectTitle}}</span>
    </div>
    
    <div class="detail-row">
      <span class="detail-label">
        {{#if (eq language 'bg')}}
          Номер на предложение:
        {{else}}
          Proposal ID:
        {{/if}}
      </span>
      <span class="detail-value proposal-id">{{proposalId}}</span>
    </div>
    
    <div class="detail-row">
      <span class="detail-label">
        {{#if (eq language 'bg')}}
          Сума:
        {{else}}
          Amount:
        {{/if}}
      </span>
      <span class="detail-value amount">{{amount}} {{currency}}</span>
    </div>
  </div>

  <div class="message">
    <p><strong>
      {{#if (eq language 'bg')}}
        Важна информация:
      {{else}}
        Important Information:
      {{/if}}
    </strong></p>
    <ul>
      {{#if (eq language 'bg')}}
        <li>Фактурата е приложена като PDF файл към този имейл</li>
        <li>Моля, запазете фактурата за Вашите счетоводни нужди</li>
        <li>При въпроси, моля свържете се с нас</li>
      {{else}}
        <li>The invoice is attached as a PDF file to this email</li>
        <li>Please keep this invoice for your accounting records</li>
        <li>Contact us if you have any questions</li>
      {{/if}}
    </ul>
  </div>

  <p>
    {{#if (eq language 'bg')}}
      Благодарим Ви за доверието!
    {{else}}
      Thank you for your business!
    {{/if}}
  </p>

  <div class="footer">
    <p><strong>{{companyName}}</strong></p>
    <p>
      {{#if (eq language 'bg')}}
        Този имейл е изпратен автоматично. Моля, не отговаряйте директно на този имейл.
      {{else}}
        This email was sent automatically. Please do not reply directly to this email.
      {{/if}}
    </p>
    <p>
      {{#if (eq language 'bg')}}
        За въпроси се свържете с нас на: <EMAIL>
      {{else}}
        For questions, contact us at: <EMAIL>
      {{/if}}
    </p>
  </div>
</div>
</body>
</html>

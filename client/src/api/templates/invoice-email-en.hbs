<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Invoice {{invoiceNumber}}</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 600px;
      margin: 0 auto;
      padding: 20px;
      background-color: #f9f9f9;
    }

    .container {
      background-color: white;
      padding: 30px;
      border-radius: 10px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .header {
      text-align: center;
      border-bottom: 3px solid #007bff;
      padding-bottom: 20px;
      margin-bottom: 30px;
    }

    .logo {
      font-size: 28px;
      font-weight: bold;
      color: #007bff;
      margin-bottom: 10px;
    }

    .invoice-title {
      font-size: 24px;
      color: #333;
      margin: 20px 0;
    }

    .invoice-details {
      background-color: #f8f9fa;
      padding: 20px;
      border-radius: 8px;
      margin: 20px 0;
    }

    .detail-row {
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;
      padding: 5px 0;
    }

    .detail-label {
      font-weight: bold;
      color: #555;
    }

    .detail-value {
      color: #333;
    }

    .amount {
      font-size: 20px;
      font-weight: bold;
      color: #28a745;
    }

    .message {
      margin: 25px 0;
      padding: 20px;
      background-color: #e3f2fd;
      border-left: 4px solid #007bff;
      border-radius: 4px;
    }

    .footer {
      margin-top: 30px;
      padding-top: 20px;
      border-top: 1px solid #eee;
      text-align: center;
      color: #666;
      font-size: 14px;
    }

    .button {
      display: inline-block;
      padding: 12px 24px;
      background-color: #007bff;
      color: white;
      text-decoration: none;
      border-radius: 5px;
      margin: 15px 0;
      font-weight: bold;
    }

    .button:hover {
      background-color: #0056b3;
    }

    @media (max-width: 600px) {
      body {
        padding: 10px;
      }

      .container {
        padding: 20px;
      }

      .detail-row {
        flex-direction: column;
      }
    }
  </style>
</head>
<body>
<div class="container">
  <div class="header">
    <div class="logo">{{companyName}}</div>
    <h1 class="invoice-title">Invoice {{invoiceNumber}}</h1>
  </div>

  <p>Dear {{clientName}},</p>

  <p>Please find attached the invoice for project <strong>{{projectTitle}}</strong>.</p>

  <div class="invoice-details">
    <div class="detail-row">
      <span class="detail-label">Invoice Number:</span>
      <span class="detail-value">{{invoiceNumber}}</span>
    </div>
    <div class="detail-row">
      <span class="detail-label">Payment Type:</span>
      <span class="detail-value">
        {{#if (eq invoiceType 'initial')}}
          Initial Payment
        {{else}}
          Final Payment
        {{/if}}
      </span>
    </div>
    <div class="detail-row">
      <span class="detail-label">Project:</span>
      <span class="detail-value">{{projectTitle}}</span>
    </div>
    <div class="detail-row">
      <span class="detail-label">Proposal ID:</span>
      <span class="detail-value">{{proposalId}}</span>
    </div>
    <div class="detail-row">
      <span class="detail-label">Amount:</span>
      <span class="detail-value amount">{{amount}} {{currency}}</span>
    </div>
  </div>

  <div class="message">
    <p><strong>Important Information:</strong></p>
    <ul>
      <li>The invoice is attached as a PDF file to this email</li>
      <li>Please keep this invoice for your accounting records</li>
      <li>Contact us if you have any questions</li>
    </ul>
  </div>

  <p>Thank you for your business!</p>

  <div class="footer">
    <p><strong>{{companyName}}</strong></p>
    <p>This email was sent automatically. Please do not reply directly to this email.</p>
    <p>For questions, contact us at: <EMAIL></p>
  </div>
</div>


</body>
</html>

import {
  SendEmailCommand,
  SendRaw<PERSON>mailCommand,
  SESClient,
} from '@aws-sdk/client-ses';
import * as handlebars from 'handlebars';
import * as fs from 'fs';
import * as path from 'path';

export interface EmailAttachment {
  filename: string;
  content: Buffer;
  contentType: string;
}

export interface EmailTemplate {
  subject: string;
  html: string;
  text?: string;
}

export interface InvoiceEmailData {
  clientName: string;
  clientEmail: string;
  invoiceNumber: string;
  invoiceType: 'initial' | 'final';
  projectTitle: string;
  proposalId: string;
  amount: number;
  currency: string;
  companyName: string;
  language: 'bg' | 'en';
}

export class EmailService {
  private sesClient: SESClient;
  private fromEmail: string;
  private fromName: string;
  private templatesPath: string;

  constructor() {
    this.sesClient = new SESClient({
      region: process.env['AWS_REGION'] || 'eu-west-2',
      credentials: {
        accessKeyId: process.env['AWS_ACCESS_KEY_ID'] || '',
        secretAccessKey: process.env['AWS_SECRET_ACCESS_KEY'] || '',
      },
    });

    this.fromEmail = process.env['FROM_EMAIL'] || '<EMAIL>';
    this.fromName = process.env['FROM_NAME'] || 'Chainmatic Ltd.';

    this.templatesPath = path.resolve(process.cwd(), 'src/api/templates');

    // Register Handlebars helpers
    handlebars.registerHelper('eq', function (a: any, b: any) {
      return a === b;
    });

    // Validate required environment variables
    if (
      !process.env['AWS_ACCESS_KEY_ID'] ||
      !process.env['AWS_SECRET_ACCESS_KEY']
    ) {
      console.warn(
        'AWS SES credentials not configured. Email sending will fail.',
      );
    }
  }

  /**
   * Send email with optional attachment
   */
  async sendEmail(
    to: string,
    subject: string,
    htmlContent: string,
    textContent?: string,
    attachment?: EmailAttachment,
  ): Promise<boolean> {
    try {
      if (attachment) {
        // Send email with attachment using raw email
        return await this.sendRawEmail(
          to,
          subject,
          htmlContent,
          textContent,
          attachment,
        );
      } else {
        // Send simple email
        const command = new SendEmailCommand({
          Source: `${this.fromName} <${this.fromEmail}>`,
          Destination: {
            ToAddresses: [to],
          },
          Message: {
            Subject: {
              Data: subject,
              Charset: 'UTF-8',
            },
            Body: {
              Html: {
                Data: htmlContent,
                Charset: 'UTF-8',
              },
              Text: textContent
                ? {
                    Data: textContent,
                    Charset: 'UTF-8',
                  }
                : undefined,
            },
          },
        });

        await this.sesClient.send(command);
        return true;
      }
    } catch (error) {
      console.error('Failed to send email:', error);
      return false;
    }
  }

  /**
   * Send invoice email with PDF attachment
   */
  async sendInvoiceEmail(
    emailData: InvoiceEmailData,
    invoicePdf?: Buffer,
  ): Promise<boolean> {
    try {
      // Determine template based on language
      const templateName =
        emailData.language === 'bg' ? 'invoice-email-bg' : 'invoice-email-en';
      const template = this.loadTemplate(templateName);

      // Compile template with data
      const htmlContent = template(emailData);

      // Create subject
      const subjectKey =
        emailData.language === 'bg'
          ? `Фактура ${emailData.invoiceNumber} - ${emailData.projectTitle}`
          : `Invoice ${emailData.invoiceNumber} - ${emailData.projectTitle}`;

      // Prepare attachment if PDF is provided
      let attachment: EmailAttachment | undefined;
      if (invoicePdf) {
        attachment = {
          filename: `invoice-${emailData.invoiceNumber}.pdf`,
          content: invoicePdf,
          contentType: 'application/pdf',
        };
      }

      // Send email
      const success = await this.sendEmail(
        emailData.clientEmail,
        subjectKey,
        htmlContent,
        undefined, // No text version for now
        attachment,
      );

      if (success) {
        console.log(
          `Invoice email sent successfully to ${emailData.clientEmail}`,
        );
      } else {
        console.error(
          `Failed to send invoice email to ${emailData.clientEmail}`,
        );
      }

      return success;
    } catch (error) {
      console.error('Error sending invoice email:', error);
      return false;
    }
  }

  /**
   * Test email configuration
   */
  async testConfiguration(): Promise<boolean> {
    try {
      // Check if AWS credentials are configured
      if (
        !process.env['AWS_ACCESS_KEY_ID'] ||
        !process.env['AWS_SECRET_ACCESS_KEY']
      ) {
        console.log(
          'AWS SES credentials not configured - skipping actual email test',
        );

        // Test template loading instead
        try {
          const template = this.loadTemplate('invoice-email-en');
          console.log('Email templates loaded successfully');
          return true;
        } catch (templateError) {
          console.error('Template loading failed:', templateError);
          return false;
        }
      }

      // Try to send a simple test email to verify configuration
      const testEmail = process.env['TEST_EMAIL'] || '<EMAIL>';

      const success = await this.sendEmail(
        testEmail,
        'AWS SES Configuration Test',
        '<h1>Test Email</h1><p>AWS SES is configured correctly!</p>',
        'Test Email - AWS SES is configured correctly!',
      );

      return success;
    } catch (error) {
      console.error('Email configuration test failed:', error);
      return false;
    }
  }

  /**
   * Load and compile Handlebars template
   */
  private loadTemplate(templateName: string): handlebars.TemplateDelegate {
    try {
      const templatePath = path.join(this.templatesPath, `${templateName}.hbs`);
      const templateSource = fs.readFileSync(templatePath, 'utf8');
      return handlebars.compile(templateSource);
    } catch (error) {
      console.error(`Failed to load template ${templateName}:`, error);
      throw new Error(`Template ${templateName} not found or invalid`);
    }
  }

  /**
   * Send raw email with attachment
   */
  private async sendRawEmail(
    to: string,
    subject: string,
    htmlContent: string,
    textContent?: string,
    attachment?: EmailAttachment,
  ): Promise<boolean> {
    try {
      const boundary = `----=_Part_${Date.now()}_${Math.random().toString(36)}`;

      let rawMessage = `From: ${this.fromName} <${this.fromEmail}>\r\n`;
      rawMessage += `To: ${to}\r\n`;
      rawMessage += `Subject: ${subject}\r\n`;
      rawMessage += `MIME-Version: 1.0\r\n`;
      rawMessage += `Content-Type: multipart/mixed; boundary="${boundary}"\r\n\r\n`;

      // Email body
      rawMessage += `--${boundary}\r\n`;
      rawMessage += `Content-Type: multipart/alternative; boundary="${boundary}_alt"\r\n\r\n`;

      // Text part
      if (textContent) {
        rawMessage += `--${boundary}_alt\r\n`;
        rawMessage += `Content-Type: text/plain; charset=UTF-8\r\n\r\n`;
        rawMessage += `${textContent}\r\n\r\n`;
      }

      // HTML part
      rawMessage += `--${boundary}_alt\r\n`;
      rawMessage += `Content-Type: text/html; charset=UTF-8\r\n\r\n`;
      rawMessage += `${htmlContent}\r\n\r\n`;
      rawMessage += `--${boundary}_alt--\r\n\r\n`;

      // Attachment
      if (attachment) {
        rawMessage += `--${boundary}\r\n`;
        rawMessage += `Content-Type: ${attachment.contentType}\r\n`;
        rawMessage += `Content-Disposition: attachment; filename="${attachment.filename}"\r\n`;
        rawMessage += `Content-Transfer-Encoding: base64\r\n\r\n`;
        rawMessage += attachment.content.toString('base64') + '\r\n\r\n';
      }

      rawMessage += `--${boundary}--\r\n`;

      const command = new SendRawEmailCommand({
        Source: this.fromEmail,
        Destinations: [to],
        RawMessage: {
          Data: Buffer.from(rawMessage),
        },
      });

      await this.sesClient.send(command);
      return true;
    } catch (error) {
      console.error('Failed to send raw email:', error);
      return false;
    }
  }
}
